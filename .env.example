# API Configuration
VITE_API_BASE_URL=http://localhost:5057/api

# Google OAuth Configuration
# Get your Google Client ID from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select existing one
# 3. Enable Google+ API
# 4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
# 5. Application type: Web application
# 6. Add authorized origins: http://localhost:5173 (for development)
# 7. Copy the Client ID and paste it below
VITE_GOOGLE_CLIENT_ID=YOUR_ACTUAL_GOOGLE_CLIENT_ID_HERE

# Development settings
VITE_NODE_ENV=development
